import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  SafeAreaView,
} from "react-native";
import { Stack, useLocalSearchParams, router } from "expo-router";
import {
  connect,
  type EnrichedActivity,
  type EnrichedReaction,
} from "getstream";
import {
  ArrowLeft,
  Heart,
  MessageCircle,
  Send,
  Pin,
} from "lucide-react-native";
import { Image } from "expo-image";

import { useAppContext } from "@/context/app";

const STREAM_API_KEY = process.env.EXPO_PUBLIC_STREAM_API_KEY!;
const STREAM_APP_ID = process.env.EXPO_PUBLIC_STREAM_APP_ID!;

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  isPinned?: boolean;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  own_reactions?: {
    like?: any[];
    comment?: EnrichedReaction[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

type CommentWithUser = EnrichedReaction & {
  user?: {
    id: string;
    created_at: string;
    updated_at: string;
    data: {
      name?: string;
      image?: string;
      avatarUrl?: string;
    };
  };
};

export default function PostDetail() {
  const { id: activityId, moduleConfig } = useLocalSearchParams<{
    id: string;
    moduleConfig: string;
  }>();
  const { streamToken, userId } = useAppContext();

  const [activity, setActivity] = useState<EnrichedActivityWithText | null>(
    null
  );
  const [comments, setComments] = useState<CommentWithUser[]>([]);
  const [commentText, setCommentText] = useState("");
  const [loading, setLoading] = useState(true);
  const [loadingComments, setLoadingComments] = useState(false);
  const [posting, setPosting] = useState(false);
  const [client, setClient] = useState<any>(null);
  const [moduleConfigParsed] = useState(() =>
    moduleConfig ? JSON.parse(moduleConfig) : null
  );

  const fetchActivity = useCallback(async () => {
    if (!streamToken || !activityId || !moduleConfigParsed) return;

    try {
      setLoading(true);

      // Initialize GetStream client
      const streamClient = connect(STREAM_API_KEY, streamToken, STREAM_APP_ID);
      setClient(streamClient);

      // Get the feed
      const feed = streamClient.feed(
        moduleConfigParsed.feedGroup,
        moduleConfigParsed.feedId
      );

      // Fetch activities
      const response = await feed.get({
        id_gte: activityId,
        limit: 1,
        enrich: true,
        withReactionCounts: true,
        withOwnReactions: true,
      });

      if (response.results.length > 0) {
        setActivity(response.results[0] as EnrichedActivityWithText);
        fetchComments(streamClient, response.results[0].id);
      }
    } catch (err) {
      console.error("Error fetching activity:", err);
    } finally {
      setLoading(false);
    }
  }, [streamToken, activityId, moduleConfigParsed]);

  const fetchComments = async (streamClient: any, activityId: string) => {
    setLoadingComments(true);
    try {
      const response = await streamClient.reactions.filter({
        activity_id: activityId,
        kind: "comment",
        limit: 50,
      });
      setComments(response.results as CommentWithUser[]);
    } catch (error) {
      console.error("Error fetching comments:", error);
    } finally {
      setLoadingComments(false);
    }
  };

  useEffect(() => {
    fetchActivity();
  }, [fetchActivity]);

  const handleLike = async () => {
    if (!client || !activity) return;

    try {
      const isLiked =
        activity.own_reactions?.like && activity.own_reactions.like.length > 0;

      if (isLiked) {
        // Unlike
        await client.reactions.delete(activity.own_reactions!.like![0].id);

        setActivity({
          ...activity,
          own_reactions: { ...activity.own_reactions, like: [] },
          reaction_counts: {
            ...activity.reaction_counts,
            like: Math.max((activity.reaction_counts?.like || 0) - 1, 0),
          },
        });
      } else {
        // Like
        const reaction = await client.reactions.add("like", activity.id);

        setActivity({
          ...activity,
          own_reactions: { ...activity.own_reactions, like: [reaction] },
          reaction_counts: {
            ...activity.reaction_counts,
            like: (activity.reaction_counts?.like || 0) + 1,
          },
        });
      }
    } catch (error) {
      console.error("Error toggling like:", error);
    }
  };

  const handleAddComment = async () => {
    if (!client || !activity || !commentText.trim()) return;

    setPosting(true);
    try {
      const comment = await client.reactions.add("comment", activity.id, {
        text: commentText,
      });

      // Add comment to local state with user info
      const newComment: CommentWithUser = {
        ...comment,
        user: {
          id: userId!,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          data: {
            name: "You",
          },
        },
      };

      setComments([...comments, newComment]);
      setCommentText("");

      // Update activity comment count
      setActivity({
        ...activity,
        reaction_counts: {
          ...activity.reaction_counts,
          comment: (activity.reaction_counts?.comment || 0) + 1,
        },
      });
    } catch (error) {
      console.error("Error adding comment:", error);
    } finally {
      setPosting(false);
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return date.toLocaleDateString();
  };

  const getActorName = (actor: any): string => {
    if (typeof actor === "string") {
      const parts = actor.split(":");
      return parts[parts.length - 1] || actor;
    }
    if (actor?.data?.name) return actor.data.name;
    if (actor?.id) return `User ${actor.id.substring(0, 8)}`;
    return "Unknown User";
  };

  const getActorImage = (actor: any): string | null => {
    if (actor?.data?.image) return actor.data.image;
    if (actor?.data?.avatarUrl) return actor.data.avatarUrl;
    return null;
  };

  const getActivityContent = (activity: EnrichedActivityWithText): string => {
    if (activity.message) return activity.message;
    if (activity.text) return activity.text;
    return "";
  };

  const isCreator = (actor: any): boolean => {
    if (actor?.data?.role) {
      return actor.data.role === "creator" || actor.data.role === "moderator";
    }
    return false;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#EF5252" />
        </View>
      </SafeAreaView>
    );
  }

  if (!activity) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Post not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <ArrowLeft size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Post</Text>
        <View style={{ width: 24 }} />
      </View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={0}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {/* Post Content */}
          <View style={styles.postCard}>
            <View style={styles.postHeader}>
              <View style={styles.userInfo}>
                {getActorImage(activity.actor) ? (
                  <Image
                    source={{ uri: getActorImage(activity.actor)! }}
                    style={styles.avatar}
                  />
                ) : (
                  <View style={styles.avatarPlaceholder}>
                    <Text style={styles.avatarText}>
                      {getActorName(activity.actor)[0]?.toUpperCase() || "U"}
                    </Text>
                  </View>
                )}
                <View style={styles.userMeta}>
                  <View style={styles.nameContainer}>
                    <Text style={styles.userName}>
                      {getActorName(activity.actor)}
                    </Text>
                    {isCreator(activity.actor) && (
                      <View style={styles.creatorBadge}>
                        <Text style={styles.creatorText}>Creator</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.timestamp}>
                    {formatTime(activity.time)}
                  </Text>
                </View>
              </View>
              {activity.isPinned && (
                <Pin size={20} color="#FACC15" fill="#FACC15" />
              )}
            </View>

            <View style={styles.postContent}>
              <Text style={styles.postText}>
                {getActivityContent(activity)}
              </Text>
              
              {/* Display legacy image field */}
              {activity.image && (
                <Image
                  source={{ uri: activity.image }}
                  style={styles.postImage}
                />
              )}
              
              {/* Display attachments */}
              {activity.attachments && activity.attachments.length > 0 && (
                <View style={styles.attachmentsContainer}>
                  {activity.attachments.map((attachment, index) => (
                    <View key={index} style={styles.attachmentItem}>
                      {attachment.type === "image" && attachment.image_url && (
                        <Image 
                          source={{ uri: attachment.image_url }} 
                          style={styles.postImage} 
                        />
                      )}
                      {attachment.type === "file" && attachment.asset_url && (
                        <TouchableOpacity 
                          style={styles.fileAttachment}
                          onPress={() => {
                            // Handle file opening - you might want to use a web view or external app
                            console.log("File attachment:", attachment.asset_url);
                          }}
                        >
                          <Text style={styles.fileAttachmentText}>📎 View File</Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  ))}
                </View>
              )}
            </View>

            <View style={styles.postActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleLike}
              >
                <Heart
                  size={20}
                  color={
                    activity.own_reactions?.like?.length ? "#EF5252" : "#fff"
                  }
                  fill={
                    activity.own_reactions?.like?.length
                      ? "#EF5252"
                      : "transparent"
                  }
                />
                <Text
                  style={[
                    styles.actionText,
                    activity.own_reactions?.like?.length
                      ? styles.likedText
                      : undefined,
                  ]}
                >
                  {activity.reaction_counts?.like || 0}
                </Text>
              </TouchableOpacity>

              <View style={styles.actionButton}>
                <MessageCircle size={20} color="#fff" />
                <Text style={styles.actionText}>
                  {activity.reaction_counts?.comment || 0}
                </Text>
              </View>
            </View>
          </View>

          {/* Comments Section */}
          <View style={styles.commentsSection}>
            <Text style={styles.commentsTitle}>Comments</Text>

            {loadingComments ? (
              <ActivityIndicator
                size="small"
                color="#EF5252"
                style={{ marginTop: 20 }}
              />
            ) : comments.length === 0 ? (
              <Text style={styles.noCommentsText}>
                No comments yet. Be the first to comment!
              </Text>
            ) : (
              comments.map((comment) => (
                <View key={comment.id} style={styles.commentCard}>
                  <View style={styles.commentHeader}>
                    {comment.user?.data?.image ||
                    comment.user?.data?.avatarUrl ? (
                      <Image
                        source={{
                          uri:
                            comment.user.data.image ||
                            comment.user.data.avatarUrl,
                        }}
                        style={styles.commentAvatar}
                      />
                    ) : (
                      <View style={styles.commentAvatarPlaceholder}>
                        <Text style={styles.commentAvatarText}>
                          {(comment.user?.data?.name || "U")[0].toUpperCase()}
                        </Text>
                      </View>
                    )}
                    <View style={styles.commentMeta}>
                      <Text style={styles.commentUserName}>
                        {comment.user?.data?.name || "Anonymous"}
                      </Text>
                      <Text style={styles.commentTimestamp}>
                        {formatTime(comment.created_at)}
                      </Text>
                    </View>
                  </View>
                  <Text style={styles.commentText}>
                    {(comment.data as any)?.text || ""}
                  </Text>
                </View>
              ))
            )}
          </View>
        </ScrollView>

        {/* Comment Input */}
        <View style={styles.commentInputContainer}>
          <TextInput
            style={styles.commentInput}
            placeholder="Write a comment..."
            placeholderTextColor="#9A9A9A"
            value={commentText}
            onChangeText={setCommentText}
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              !commentText.trim() && styles.sendButtonDisabled,
            ]}
            onPress={handleAddComment}
            disabled={!commentText.trim() || posting}
          >
            {posting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Send size={20} color="#fff" />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#242424",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
  },
  errorText: {
    color: "#9A9A9A",
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  postCard: {
    backgroundColor: "#171D23",
    padding: 16,
    marginBottom: 16,
  },
  postHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#EF5252",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "600",
  },
  userMeta: {
    gap: 2,
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  userName: {
    fontSize: 16,
    fontWeight: "700",
    color: "#fff",
  },
  creatorBadge: {
    backgroundColor: "#EF5252",
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  creatorText: {
    fontSize: 10,
    fontWeight: "500",
    color: "#fff",
  },
  timestamp: {
    fontSize: 14,
    color: "#9A9A9A",
  },
  postContent: {
    marginBottom: 16,
  },
  postText: {
    fontSize: 16,
    color: "#D9D9D9",
    lineHeight: 24,
  },
  postImage: {
    width: "100%",
    height: 250,
    borderRadius: 8,
    marginTop: 12,
  },
  postActions: {
    flexDirection: "row",
    gap: 24,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#242424",
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  actionText: {
    fontSize: 16,
    fontWeight: "700",
    color: "rgba(255, 255, 255, 0.8)",
  },
  likedText: {
    color: "#EF5252",
  },
  commentsSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  commentsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 16,
  },
  noCommentsText: {
    color: "#9A9A9A",
    fontSize: 14,
    textAlign: "center",
    marginTop: 20,
  },
  commentCard: {
    backgroundColor: "#171D23",
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  commentHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
    marginBottom: 8,
  },
  commentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  commentAvatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#333",
    justifyContent: "center",
    alignItems: "center",
  },
  commentAvatarText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  commentMeta: {
    flex: 1,
  },
  commentUserName: {
    fontSize: 14,
    fontWeight: "600",
    color: "#fff",
  },
  commentTimestamp: {
    fontSize: 12,
    color: "#9A9A9A",
  },
  commentText: {
    fontSize: 14,
    color: "#D9D9D9",
    lineHeight: 20,
  },
  commentInputContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#242424",
    backgroundColor: "#000",
  },
  commentInput: {
    flex: 1,
    backgroundColor: "#171D23",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 12,
    color: "#fff",
    fontSize: 14,
    maxHeight: 100,
  },
  sendButton: {
    backgroundColor: "#EF5252",
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  attachmentsContainer: {
    marginTop: 12,
  },
  attachmentItem: {
    marginBottom: 8,
  },
  fileAttachment: {
    backgroundColor: "#333",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  fileAttachmentText: {
    color: "#9A9A9A",
    fontSize: 14,
  },
});
